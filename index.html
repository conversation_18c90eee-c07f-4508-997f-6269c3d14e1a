<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hype Me Up - Your Daily Dose of Positivity</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #0f0f23;
            --secondary-bg: #1a1a2e;
            --accent-color: #16213e;
            --text-primary: #eee;
            --text-secondary: #a0a0a0;
            --glow-color: #00d4ff;
            --card-bg: rgba(255, 255, 255, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Animated background particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--glow-color);
            border-radius: 50%;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
        }

        .main-container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: slideDown 1s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .title {
            font-family: 'Playfair Display', serif;
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--glow-color), #ff6b6b, #4ecdc4);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 10px;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            font-weight: 300;
        }

        .mood-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            max-width: 800px;
            width: 100%;
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .mood-card {
            background: var(--card-bg);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .mood-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .mood-card:hover::before {
            left: 100%;
        }

        .mood-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
            border-color: var(--glow-color);
        }

        .mood-card.active {
            background: linear-gradient(135deg, var(--glow-color), #4ecdc4);
            color: var(--primary-bg);
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 212, 255, 0.4);
        }

        .mood-emoji {
            font-size: 2rem;
            margin-bottom: 8px;
            display: block;
            filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.3));
        }

        .mood-name {
            font-size: 0.95rem;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .mood-description {
            font-size: 0.75rem;
            opacity: 0.7;
            line-height: 1.3;
        }

        .quote-section {
            max-width: 800px;
            width: 100%;
            text-align: center;
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .quote-card {
            background: var(--card-bg);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 50px 40px;
            backdrop-filter: blur(15px);
            position: relative;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .quote-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, var(--glow-color), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0.1;
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        .quote-text {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            line-height: 1.6;
            font-weight: 400;
            position: relative;
            z-index: 2;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .quote-text.show {
            opacity: 1;
            transform: scale(1);
        }

        .quote-text::before,
        .quote-text::after {
            content: '"';
            font-size: 3rem;
            color: var(--glow-color);
            opacity: 0.5;
            position: absolute;
            font-family: 'Playfair Display', serif;
        }

        .quote-text::before {
            top: -20px;
            left: -30px;
        }

        .quote-text::after {
            bottom: -40px;
            right: -30px;
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .btn {
            background: linear-gradient(45deg, var(--glow-color), #4ecdc4);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 500;
            color: var(--primary-bg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
                margin-bottom: 8px;
            }

            .subtitle {
                font-size: 1rem;
            }

            .header {
                margin-bottom: 40px;
            }

            .mood-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 12px;
                margin-bottom: 30px;
            }

            .mood-card {
                padding: 15px 10px;
                min-height: 100px;
            }

            .mood-emoji {
                font-size: 1.5rem;
                margin-bottom: 6px;
            }

            .mood-name {
                font-size: 0.85rem;
                margin-bottom: 3px;
            }

            .mood-description {
                font-size: 0.7rem;
            }

            .quote-card {
                padding: 30px 20px;
                min-height: 160px;
            }

            .quote-text {
                font-size: 1.3rem;
            }

            .quote-text::before,
            .quote-text::after {
                font-size: 2rem;
            }

            .quote-text::before {
                top: -15px;
                left: -20px;
            }

            .quote-text::after {
                bottom: -25px;
                right: -20px;
            }

            .controls {
                gap: 12px;
            }

            .btn {
                padding: 12px 20px;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 2rem;
            }

            .mood-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .mood-card {
                padding: 12px 8px;
                min-height: 90px;
            }

            .mood-emoji {
                font-size: 1.3rem;
            }

            .mood-name {
                font-size: 0.8rem;
            }

            .mood-description {
                font-size: 0.65rem;
            }

            .quote-card {
                padding: 25px 15px;
                min-height: 140px;
            }

            .quote-text {
                font-size: 1.1rem;
            }

            .quote-text::before,
            .quote-text::after {
                font-size: 1.5rem;
            }

            .controls {
                gap: 10px;
                flex-direction: column;
                align-items: center;
            }

            .btn {
                padding: 10px 18px;
                font-size: 0.8rem;
                min-width: 140px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated background particles -->
    <div class="particles" id="particles"></div>

    <div class="main-container">
        <header class="header">
            <h1 class="title">Hype Me Up</h1>
            <p class="subtitle">Your daily dose of positivity and strength</p>
        </header>

        <section class="quote-section">
            <div class="quote-card">
                <div class="quote-text" id="quoteText"></div>
            </div>
        </section>

        <div class="mood-grid" id="moodGrid">
            <div class="mood-card" data-mood="anxious">
                <span class="mood-emoji">🌊</span>
                <div class="mood-name">Anxious</div>
                <div class="mood-description">Feeling overwhelmed or worried</div>
            </div>
            <div class="mood-card" data-mood="fear">
                <span class="mood-emoji">🌙</span>
                <div class="mood-name">Fear of Loss</div>
                <div class="mood-description">Afraid of losing something precious</div>
            </div>
            <div class="mood-card" data-mood="frustrated">
                <span class="mood-emoji">🔥</span>
                <div class="mood-name">Frustrated</div>
                <div class="mood-description">Feeling stuck or irritated</div>
            </div>
            <div class="mood-card" data-mood="tired">
                <span class="mood-emoji">💤</span>
                <div class="mood-name">Tired</div>
                <div class="mood-description">Exhausted and need rest</div>
            </div>
            <div class="mood-card" data-mood="lonely">
                <span class="mood-emoji">⭐</span>
                <div class="mood-name">Lonely</div>
                <div class="mood-description">Feeling isolated or disconnected</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="newQuoteBtn">New Quote</button>
            <button class="btn" id="randomMoodBtn">Random Mood</button>
            <button class="btn" id="breatheBtn">Take a Breath</button>
        </div>
    </div>

    <script>
        // Enhanced quotes with more variety and depth
        const quotes = {
            anxious: [
                "Breathe in peace, breathe out worry. You are safe in this moment.",
                "Your anxiety is not a weakness—it's your mind trying to protect you.",
                "Like clouds passing through the sky, this feeling will pass too.",
                "You've weathered storms before. You have the strength within you.",
                "One breath, one heartbeat, one moment at a time. You've got this.",
                "The present moment is your sanctuary. Rest here.",
                "Your nervous system is learning. Be patient with yourself.",
                "Anxiety whispers lies. Your strength speaks truth.",
                "You are not your thoughts. You are the observer of them.",
                "Ground yourself: 5 things you see, 4 you hear, 3 you touch, 2 you smell, 1 you taste."
            ],
            frustrated: [
                "This obstacle is not a wall—it's a stepping stone in disguise.",
                "Your frustration shows how much you care. That's beautiful.",
                "Progress isn't always visible, but it's always happening.",
                "Every master was once a disaster. Keep going.",
                "The path isn't straight, but it's yours to walk.",
                "Your current chapter is not your whole story.",
                "Frustration is the feeling before breakthrough.",
                "You're not behind in life. You're exactly where you need to be.",
                "The river that moves mountains does so one drop at a time.",
                "Your persistence is your superpower, even when it doesn't feel like it."
            ],
            tired: [
                "Rest is not a reward for work completed—it's a requirement for work continued.",
                "Your worth is not measured by your productivity today.",
                "Even the strongest trees bend in the wind. It's okay to rest.",
                "You don't have to earn your rest. You deserve it simply by being.",
                "Tired is not lazy. Tired is human.",
                "Your body is asking for what it needs. Listen with compassion.",
                "Sometimes the most revolutionary thing you can do is rest.",
                "You've been strong for so long. It's okay to be soft now.",
                "Rest is not giving up—it's preparing for what's next.",
                "Your energy will return. For now, just be."
            ],
            lonely: [
                "You are never truly alone—you carry the love of everyone who has ever cared for you.",
                "Loneliness is not a sign of being unloved—it's a sign of being human.",
                "Even in solitude, you are connected to the vast web of existence.",
                "Your presence matters more than you know to people you may never meet.",
                "The moon shines alone but lights up the entire night sky.",
                "Solitude can be a gift when you learn to enjoy your own company.",
                "You are worthy of connection, starting with the connection to yourself.",
                "Every person you've ever loved lives on in your heart.",
                "Loneliness is temporary. Your capacity for love is eternal.",
                "You are a unique note in the symphony of existence."
            ],
            fear: [
                "Courage is not the absence of fear—it's feeling fear and moving forward anyway.",
                "What you're afraid of losing was never truly yours to control.",
                "Fear is often excitement without breath. Take a deep breath.",
                "You've survived 100% of your difficult days so far.",
                "Every ending creates space for a new beginning.",
                "Your resilience is stronger than your fear.",
                "Fear is a compass pointing toward what matters most to you.",
                "You are more capable than you believe, stronger than you seem.",
                "Loss teaches us what truly cannot be taken away.",
                "The unknown is not empty—it's full of infinite possibilities."
            ]
        };

        // Dynamic mood themes with more sophisticated color schemes
        const moodThemes = {
            anxious: {
                primary: '#0a1628',
                secondary: '#1e3a8a',
                accent: '#3b82f6',
                glow: '#60a5fa'
            },
            frustrated: {
                primary: '#1f1419',
                secondary: '#7c2d12',
                accent: '#ea580c',
                glow: '#fb923c'
            },
            tired: {
                primary: '#0f1419',
                secondary: '#164e63',
                accent: '#0891b2',
                glow: '#22d3ee'
            },
            lonely: {
                primary: '#1a1625',
                secondary: '#581c87',
                accent: '#a855f7',
                glow: '#c084fc'
            },
            fear: {
                primary: '#191a1f',
                secondary: '#374151',
                accent: '#6b7280',
                glow: '#9ca3af'
            }
        };

        // App state
        let currentMood = null;
        let isBreathing = false;

        // DOM elements
        const quoteText = document.getElementById('quoteText');
        const moodCards = document.querySelectorAll('.mood-card');
        const newQuoteBtn = document.getElementById('newQuoteBtn');
        const randomMoodBtn = document.getElementById('randomMoodBtn');
        const breatheBtn = document.getElementById('breatheBtn');
        const particles = document.getElementById('particles');

        // Utility functions
        function getRandomItem(array) {
            return array[Math.floor(Math.random() * array.length)];
        }

        function getRandomMood() {
            return getRandomItem(Object.keys(quotes));
        }

        function getRandomQuote(mood) {
            return getRandomItem(quotes[mood]);
        }

        // Visual effects
        function createParticles() {
            particles.innerHTML = '';
            const particleCount = window.innerWidth < 768 ? 30 : 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (4 + Math.random() * 4) + 's';
                particles.appendChild(particle);
            }
        }

        function updateTheme(mood) {
            const theme = moodThemes[mood];
            const root = document.documentElement;

            root.style.setProperty('--primary-bg', theme.primary);
            root.style.setProperty('--secondary-bg', theme.secondary);
            root.style.setProperty('--accent-color', theme.accent);
            root.style.setProperty('--glow-color', theme.glow);
        }

        function displayQuote(quote) {
            quoteText.classList.remove('show');

            setTimeout(() => {
                quoteText.textContent = quote;
                quoteText.classList.add('show');
            }, 300);
        }

        function setActiveMood(mood) {
            // Remove active class from all cards
            moodCards.forEach(card => card.classList.remove('active'));

            // Add active class to selected mood
            const activeCard = document.querySelector(`[data-mood="${mood}"]`);
            if (activeCard) {
                activeCard.classList.add('active');
            }

            currentMood = mood;
            updateTheme(mood);

            const quote = getRandomQuote(mood);
            displayQuote(quote);
        }

        // Breathing exercise
        function startBreathingExercise() {
            if (isBreathing) return;

            isBreathing = true;
            breatheBtn.textContent = 'Breathing...';
            breatheBtn.style.pointerEvents = 'none';

            const breathingQuotes = [
                "Breathe in calm, breathe out tension...",
                "Inhale peace, exhale worry...",
                "You are safe. You are enough. You are loved.",
                "This moment is all you need to handle.",
                "Let your breath anchor you to the present."
            ];

            displayQuote(getRandomItem(breathingQuotes));

            // Reset after 5 seconds
            setTimeout(() => {
                isBreathing = false;
                breatheBtn.textContent = 'Take a Breath';
                breatheBtn.style.pointerEvents = 'auto';

                if (currentMood) {
                    displayQuote(getRandomQuote(currentMood));
                }
            }, 5000);
        }

        // Event listeners
        moodCards.forEach(card => {
            card.addEventListener('click', () => {
                const mood = card.dataset.mood;
                setActiveMood(mood);
            });
        });

        newQuoteBtn.addEventListener('click', () => {
            if (currentMood) {
                const quote = getRandomQuote(currentMood);
                displayQuote(quote);
            }
        });

        randomMoodBtn.addEventListener('click', () => {
            const randomMood = getRandomMood();
            setActiveMood(randomMood);
        });

        breatheBtn.addEventListener('click', startBreathingExercise);

        // Initialize app
        function initApp() {
            createParticles();

            // Start with a random mood
            const initialMood = getRandomMood();
            setActiveMood(initialMood);

            // Show welcome message briefly
            setTimeout(() => {
                displayQuote("Welcome. Take a moment to choose how you're feeling, or let me surprise you with some positivity.");
            }, 1000);

            // Replace with mood-specific quote after 3 seconds
            setTimeout(() => {
                if (currentMood) {
                    displayQuote(getRandomQuote(currentMood));
                }
            }, 4000);
        }

        // Handle window resize for particles
        window.addEventListener('resize', createParticles);

        // Start the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>