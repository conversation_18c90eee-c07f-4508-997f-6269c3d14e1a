<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hype Me Up - Motivational Quotes</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            transition: all 0.5s ease;
            background: linear-gradient(135deg, #A8DADC 0%, #457B9D 100%);
            color: #1D3557;
        }

        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .quote-container {
            margin: 40px 0;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quote {
            font-size: 1.5rem;
            font-weight: 300;
            line-height: 1.6;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
            max-width: 600px;
        }

        .quote.show {
            opacity: 1;
            transform: translateY(0);
        }

        .mood-selector {
            margin: 30px 0;
        }

        .mood-selector label {
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 15px;
            display: block;
            opacity: 0.8;
        }

        select {
            padding: 12px 20px;
            font-size: 1rem;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            cursor: pointer;
            outline: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        select:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        select option {
            background: #fff;
            color: #333;
        }

        .button {
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 400;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .footer {
            margin-top: 40px;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 20px;
            }

            .logo {
                font-size: 2rem;
                margin-bottom: 30px;
            }

            .quote {
                font-size: 1.2rem;
            }

            .quote-container {
                min-height: 150px;
                margin: 30px 0;
            }
        }

        @media (max-width: 480px) {
            .quote {
                font-size: 1.1rem;
            }

            .button {
                padding: 10px 20px;
                font-size: 0.9rem;
                margin: 10px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="logo">Hype Me Up ✨</h1>
        
        <div class="quote-container">
            <div class="quote" id="quote"></div>
        </div>

        <div class="mood-selector">
            <label for="mood">How are you feeling today?</label>
            <select id="mood">
                <option value="">Choose your mood...</option>
                <option value="anxious">Anxious</option>
                <option value="fear">Fear of Loss</option>
                <option value="frustrated">Frustrated</option>
                <option value="tired">Tired</option>
                <option value="lonely">Lonely</option>
            </select>
        </div>

        <button class="button" id="showAnother">Show Another Quote</button>
        <button class="button" id="randomMood">Surprise Me</button>

        <div class="footer">
            Take a moment to breathe. You've got this. 💙
        </div>
    </div>

    <script>
        const quotes = {
            anxious: [
                "You can't stop the waves, but you can learn to surf. – Jon Kabat-Zinn",
                "Take a deep breath. You're doing better than you think.",
                "Your feelings are valid. And they will pass.",
                "Anxiety is not your enemy. It's your body trying to protect you.",
                "One breath at a time. One moment at a time. You're safe.",
                "This feeling is temporary. You are permanent.",
                "Your mind is like water. When agitated, it's hard to see. When calm, everything becomes clear.",
                "You've survived every anxious moment so far. You're stronger than you know."
            ],
            frustrated: [
                "This feeling is temporary. You are not.",
                "Frustration means you're close to understanding.",
                "Even slow progress is progress.",
                "Every expert was once a beginner. Every pro was once an amateur.",
                "The obstacle is the path. Keep going.",
                "Your current situation is not your final destination.",
                "Diamonds are formed under pressure. So are you.",
                "It's okay to feel frustrated. It means you care deeply."
            ],
            tired: [
                "Rest is productive too.",
                "You've done enough for today. Be proud.",
                "Breathe. Sleep. Try again tomorrow.",
                "Your worth isn't measured by your productivity.",
                "Sometimes the most productive thing you can do is rest.",
                "Tired is not a character flaw. It's a signal to slow down.",
                "You don't have to be 'on' all the time. It's okay to pause.",
                "Self-care isn't selfish. It's necessary."
            ],
            lonely: [
                "You're not as alone as you feel.",
                "Someone out there cares about you deeply.",
                "Even the moon feels lonely sometimes—and still it shines.",
                "Loneliness is not a sign of weakness. It's a sign of being human.",
                "You matter more than you know to people you may never meet.",
                "Connection starts with connecting to yourself first.",
                "Your presence in this world makes a difference.",
                "Solitude can be a gift when you learn to enjoy your own company."
            ],
            fear: [
                "Fear is a sign you're about to grow.",
                "You've survived 100% of your worst days.",
                "Courage isn't the absence of fear, but moving forward anyway.",
                "What you're afraid of losing was never truly yours to begin with.",
                "Loss teaches us what truly matters.",
                "Every ending is also a beginning in disguise.",
                "You are more resilient than you realize.",
                "Fear is just excitement without breath. Breathe."
            ]
        };

        const moodColors = {
            anxious: {
                background: 'linear-gradient(135deg, #A8DADC 0%, #457B9D 100%)',
                color: '#1D3557'
            },
            fear: {
                background: 'linear-gradient(135deg, #CBAACB 0%, #8E44AD 100%)',
                color: '#2C2C54'
            },
            frustrated: {
                background: 'linear-gradient(135deg, #FFADAD 0%, #FF6B6B 100%)',
                color: '#6A0572'
            },
            tired: {
                background: 'linear-gradient(135deg, #E0F7FA 0%, #4FC3F7 100%)',
                color: '#0288D1'
            },
            lonely: {
                background: 'linear-gradient(135deg, #D6EAF8 0%, #5DADE2 100%)',
                color: '#154360'
            }
        };

        let currentMood = '';
        const quoteElement = document.getElementById('quote');
        const moodSelect = document.getElementById('mood');
        const showAnotherBtn = document.getElementById('showAnother');
        const randomMoodBtn = document.getElementById('randomMood');

        function getRandomQuote(mood) {
            const moodQuotes = quotes[mood];
            return moodQuotes[Math.floor(Math.random() * moodQuotes.length)];
        }

        function getRandomMood() {
            const moods = Object.keys(quotes);
            return moods[Math.floor(Math.random() * moods.length)];
        }

        function displayQuote(quote) {
            quoteElement.classList.remove('show');
            
            setTimeout(() => {
                quoteElement.textContent = quote;
                quoteElement.classList.add('show');
            }, 250);
        }

        function changeMood(mood) {
            currentMood = mood;
            const colors = moodColors[mood];
            
            document.body.style.background = colors.background;
            document.body.style.color = colors.color;
            
            const quote = getRandomQuote(mood);
            displayQuote(quote);
        }

        function initializeApp() {
            const randomMood = getRandomMood();
            currentMood = randomMood;
            moodSelect.value = randomMood;
            changeMood(randomMood);
        }

        // Event listeners
        moodSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                changeMood(e.target.value);
            }
        });

        showAnotherBtn.addEventListener('click', () => {
            if (currentMood) {
                const quote = getRandomQuote(currentMood);
                displayQuote(quote);
            }
        });

        randomMoodBtn.addEventListener('click', () => {
            const randomMood = getRandomMood();
            moodSelect.value = randomMood;
            changeMood(randomMood);
        });

        // Initialize the app
        initializeApp();
    </script>
</body>
</html>